// Simple test script for HTML sanitization functionality
// Run with: node test-html-sanitizer.js

const { JSDOM } = require('jsdom');

// Mock DOM environment for DOMPurify
const dom = new JSDOM('<!DOCTYPE html><html><body></body></html>');
global.window = dom.window;
global.document = dom.window.document;

// Import our sanitization functions
const DOMPurify = require('dompurify')(dom.window);

// Recreate the sanitization logic from our utility
const AI_ASSISTANT_SANITIZE_CONFIG = {
  ALLOWED_TAGS: [
    'p', 'br', 'div', 'span',
    'strong', 'b', 'em', 'i', 'u',
    'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
    'ul', 'ol', 'li',
    'a',
    'code', 'pre',
    'blockquote',
    'hr',
  ],
  ALLOWED_ATTR: [
    'href', 'target', 'rel',
    'class',
    'data-*',
  ],
  ALLOW_DATA_ATTR: true,
  ALLOW_UNKNOWN_PROTOCOLS: false,
  SANITIZE_DOM: true,
  KEEP_CONTENT: true,
  FORBID_ATTR: ['style', 'onclick', 'onload', 'onerror'],
  FORBID_TAGS: ['script', 'iframe', 'object', 'embed', 'form', 'input'],
};

function sanitizeAIAssistantHTML(htmlContent) {
  if (!htmlContent || typeof htmlContent !== 'string') {
    return '';
  }

  // Add hook to sanitize href attributes
  DOMPurify.addHook('afterSanitizeAttributes', function (node) {
    if (node.tagName === 'A') {
      const href = node.getAttribute('href');
      if (href) {
        if (href.match(/^(https?:|mailto:)/i)) {
          node.setAttribute('target', '_blank');
          node.setAttribute('rel', 'noopener noreferrer');
        } else {
          node.removeAttribute('href');
        }
      }
    }
  });

  const sanitized = DOMPurify.sanitize(htmlContent, AI_ASSISTANT_SANITIZE_CONFIG);
  DOMPurify.removeAllHooks();
  return sanitized;
}

function processAIAssistantContent(content) {
  if (!content || typeof content !== 'string') {
    return '';
  }

  if (!content.includes('<')) {
    const withBreaks = content
      .replace(/\n\n+/g, '</p><p>')
      .replace(/\n/g, '<br>');
    return `<p>${withBreaks}</p>`;
  }

  return sanitizeAIAssistantHTML(content);
}

// Test cases
const testCases = [
  {
    name: 'Plain Text',
    input: 'This is a simple plain text response from the AI assistant.',
    expected: '<p>This is a simple plain text response from the AI assistant.</p>'
  },
  {
    name: 'Text with Line Breaks',
    input: 'Line 1\nLine 2\n\nNew paragraph',
    expected: '<p>Line 1<br>Line 2</p><p>New paragraph</p>'
  },
  {
    name: 'Safe HTML',
    input: '<h2>Title</h2><p>Paragraph with <strong>bold</strong> and <em>italic</em></p><ul><li>Item 1</li><li>Item 2</li></ul>',
    shouldContain: ['<h2>Title</h2>', '<strong>bold</strong>', '<em>italic</em>', '<ul><li>Item 1</li><li>Item 2</li></ul>']
  },
  {
    name: 'Malicious Content',
    input: '<p>Safe content</p><script>alert("xss")</script><iframe src="evil.com"></iframe><img src="x" onerror="alert()">',
    shouldContain: ['<p>Safe content</p>'],
    shouldNotContain: ['<script>', '<iframe>', 'onerror']
  },
  {
    name: 'Links',
    input: '<a href="https://safe.com">Safe Link</a><a href="javascript:alert()">Dangerous Link</a><a href="mailto:<EMAIL>">Email</a>',
    shouldContain: ['href="https://safe.com"', 'target="_blank"', 'rel="noopener noreferrer"', 'href="mailto:<EMAIL>"'],
    shouldNotContain: ['javascript:']
  }
];

console.log('🧪 Testing AI Assistant HTML Sanitization\n');

let passedTests = 0;
let totalTests = testCases.length;

testCases.forEach((testCase, index) => {
  console.log(`Test ${index + 1}: ${testCase.name}`);
  console.log(`Input: ${testCase.input}`);
  
  const result = processAIAssistantContent(testCase.input);
  console.log(`Output: ${result}`);
  
  let passed = true;
  
  if (testCase.expected) {
    if (result === testCase.expected) {
      console.log('✅ Expected output matches');
    } else {
      console.log('❌ Expected output does not match');
      console.log(`Expected: ${testCase.expected}`);
      passed = false;
    }
  }
  
  if (testCase.shouldContain) {
    testCase.shouldContain.forEach(item => {
      if (result.includes(item)) {
        console.log(`✅ Contains: ${item}`);
      } else {
        console.log(`❌ Missing: ${item}`);
        passed = false;
      }
    });
  }
  
  if (testCase.shouldNotContain) {
    testCase.shouldNotContain.forEach(item => {
      if (!result.includes(item)) {
        console.log(`✅ Correctly excludes: ${item}`);
      } else {
        console.log(`❌ Incorrectly includes: ${item}`);
        passed = false;
      }
    });
  }
  
  if (passed) {
    passedTests++;
    console.log('🎉 Test PASSED\n');
  } else {
    console.log('💥 Test FAILED\n');
  }
});

console.log(`\n📊 Test Results: ${passedTests}/${totalTests} tests passed`);

if (passedTests === totalTests) {
  console.log('🎉 All tests passed! HTML sanitization is working correctly.');
} else {
  console.log('⚠️  Some tests failed. Please review the implementation.');
}
