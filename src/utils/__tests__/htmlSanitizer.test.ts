import { 
  processAIAssistantContent, 
  sanitizeAIAssistantHTML, 
  containsHTML, 
  stripHTML 
} from '../htmlSanitizer';

describe('HTML Sanitizer', () => {
  describe('processAIAssistantContent', () => {
    it('should convert plain text to HTML with paragraphs', () => {
      const plainText = 'Hello world\n\nThis is a new paragraph';
      const result = processAIAssistantContent(plainText);
      expect(result).toBe('<p>Hello world</p><p>This is a new paragraph</p>');
    });

    it('should convert single line breaks to <br> tags', () => {
      const plainText = 'Line 1\nLine 2\nLine 3';
      const result = processAIAssistantContent(plainText);
      expect(result).toBe('<p>Line 1<br>Line 2<br>Line 3</p>');
    });

    it('should sanitize HTML content', () => {
      const htmlContent = '<p>Safe content</p><script>alert("xss")</script>';
      const result = processAIAssistantContent(htmlContent);
      expect(result).not.toContain('<script>');
      expect(result).toContain('<p>Safe content</p>');
    });

    it('should handle empty content', () => {
      expect(processAIAssistantContent('')).toBe('');
      expect(processAIAssistantContent(null as any)).toBe('');
      expect(processAIAssistantContent(undefined as any)).toBe('');
    });
  });

  describe('sanitizeAIAssistantHTML', () => {
    it('should allow safe HTML elements', () => {
      const safeHTML = '<h1>Title</h1><p>Paragraph with <strong>bold</strong> and <em>italic</em></p><ul><li>Item 1</li><li>Item 2</li></ul>';
      const result = sanitizeAIAssistantHTML(safeHTML);
      expect(result).toContain('<h1>Title</h1>');
      expect(result).toContain('<strong>bold</strong>');
      expect(result).toContain('<em>italic</em>');
      expect(result).toContain('<ul><li>Item 1</li><li>Item 2</li></ul>');
    });

    it('should remove dangerous HTML elements', () => {
      const dangerousHTML = '<p>Safe</p><script>alert("xss")</script><iframe src="evil.com"></iframe>';
      const result = sanitizeAIAssistantHTML(dangerousHTML);
      expect(result).toContain('<p>Safe</p>');
      expect(result).not.toContain('<script>');
      expect(result).not.toContain('<iframe>');
    });

    it('should sanitize link protocols', () => {
      const htmlWithLinks = '<a href="https://safe.com">Safe Link</a><a href="javascript:alert()">Dangerous Link</a>';
      const result = sanitizeAIAssistantHTML(htmlWithLinks);
      expect(result).toContain('href="https://safe.com"');
      expect(result).not.toContain('javascript:');
    });

    it('should remove inline styles and event handlers', () => {
      const htmlWithDangerousAttrs = '<p style="color: red" onclick="alert()">Text</p>';
      const result = sanitizeAIAssistantHTML(htmlWithDangerousAttrs);
      expect(result).not.toContain('style=');
      expect(result).not.toContain('onclick=');
      expect(result).toContain('<p>Text</p>');
    });
  });

  describe('containsHTML', () => {
    it('should detect HTML content', () => {
      expect(containsHTML('<p>HTML content</p>')).toBe(true);
      expect(containsHTML('Plain text')).toBe(false);
      expect(containsHTML('Text with < symbol but no tags')).toBe(true);
      expect(containsHTML('')).toBe(false);
    });
  });

  describe('stripHTML', () => {
    it('should remove all HTML tags', () => {
      const htmlContent = '<h1>Title</h1><p>Paragraph with <strong>bold</strong> text</p>';
      const result = stripHTML(htmlContent);
      expect(result).toBe('TitleParagraph with bold text');
      expect(result).not.toContain('<');
      expect(result).not.toContain('>');
    });

    it('should handle empty content', () => {
      expect(stripHTML('')).toBe('');
      expect(stripHTML(null as any)).toBe('');
    });
  });
});

// Example test data for manual testing
export const TEST_AI_RESPONSES = {
  plainText: 'This is a simple plain text response from the AI assistant.',
  
  withLineBreaks: `This is line 1
This is line 2

This is a new paragraph after double line break`,
  
  withBasicHTML: `<h2>AI Assistant Response</h2>
<p>Here's a response with <strong>bold text</strong> and <em>italic text</em>.</p>
<ul>
<li>First item in list</li>
<li>Second item in list</li>
<li>Third item with <code>inline code</code></li>
</ul>`,
  
  withLinks: `<p>Here are some useful links:</p>
<ul>
<li><a href="https://example.com">Safe HTTPS link</a></li>
<li><a href="mailto:<EMAIL>">Email link</a></li>
<li><a href="javascript:alert('xss')">Dangerous link (should be sanitized)</a></li>
</ul>`,
  
  withCodeBlocks: `<h3>Code Example</h3>
<p>Here's some code:</p>
<pre><code>function hello() {
  console.log("Hello, world!");
}
</code></pre>
<p>And here's some <code>inline code</code> in a sentence.</p>`,
  
  withQuotes: `<p>As someone once said:</p>
<blockquote>
"The best way to predict the future is to create it."
</blockquote>
<p>This is a great quote about innovation.</p>`,
  
  maliciousContent: `<p>This looks innocent</p>
<script>alert('XSS attack!')</script>
<iframe src="https://evil.com/steal-data"></iframe>
<img src="x" onerror="alert('Image XSS')">
<a href="javascript:alert('Link XSS')">Click me</a>
<div onclick="alert('Click XSS')">Clickable div</div>`
};
