'use client';

import React, { useState } from 'react';
import styled from 'styled-components';
import { processAIAssistantContent, sanitizeAIAssistantHTML } from '@/utils/htmlSanitizer';
import { appTheme } from '@/app/theme';

const Container = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: ${appTheme.spacing.xl};
  font-family: ${appTheme.typography.fontFamily};
`;

const Title = styled.h1`
  color: ${appTheme.colors.text.primary};
  margin-bottom: ${appTheme.spacing.xl};
  text-align: center;
`;

const Section = styled.div`
  margin-bottom: ${appTheme.spacing.xl};
  padding: ${appTheme.spacing.lg};
  border: 1px solid ${appTheme.colors.border};
  border-radius: ${appTheme.borderRadius.lg};
  background: ${appTheme.colors.background.main};
`;

const SectionTitle = styled.h2`
  color: ${appTheme.colors.text.primary};
  margin-bottom: ${appTheme.spacing.md};
  font-size: 18px;
`;

const TestInput = styled.textarea`
  width: 100%;
  min-height: 120px;
  padding: ${appTheme.spacing.md};
  border: 1px solid ${appTheme.colors.border};
  border-radius: ${appTheme.borderRadius.md};
  font-family: monospace;
  font-size: 14px;
  resize: vertical;
  margin-bottom: ${appTheme.spacing.md};
`;

const Button = styled.button`
  background: ${appTheme.colors.primary};
  color: white;
  border: none;
  padding: ${appTheme.spacing.sm} ${appTheme.spacing.md};
  border-radius: ${appTheme.borderRadius.md};
  cursor: pointer;
  margin-right: ${appTheme.spacing.sm};
  margin-bottom: ${appTheme.spacing.sm};

  &:hover {
    background: ${appTheme.colors.primaryHover};
  }
`;

const OutputContainer = styled.div`
  border: 1px solid ${appTheme.colors.border};
  border-radius: ${appTheme.borderRadius.md};
  padding: ${appTheme.spacing.md};
  background: ${appTheme.colors.background.light};
  margin-bottom: ${appTheme.spacing.md};
`;

const OutputLabel = styled.h3`
  margin: 0 0 ${appTheme.spacing.sm} 0;
  font-size: 14px;
  color: ${appTheme.colors.text.secondary};
  text-transform: uppercase;
  letter-spacing: 0.5px;
`;

const RenderedOutput = styled.div`
  background: white;
  border: 1px solid ${appTheme.colors.border};
  border-radius: ${appTheme.borderRadius.md};
  padding: ${appTheme.spacing.md};
  min-height: 60px;

  /* AI Assistant message styling */
  h1, h2, h3, h4, h5, h6 {
    margin: ${appTheme.spacing.sm} 0 ${appTheme.spacing.xs} 0;
    font-weight: 600;
    color: ${appTheme.colors.text.primary};
    line-height: 1.3;
  }

  h1 { font-size: 18px; }
  h2 { font-size: 16px; }
  h3 { font-size: 15px; }
  h4, h5, h6 { font-size: 14px; }

  p {
    margin: ${appTheme.spacing.xs} 0;
    line-height: 1.5;
  }

  p:first-child { margin-top: 0; }
  p:last-child { margin-bottom: 0; }

  strong, b {
    font-weight: 600;
    color: ${appTheme.colors.text.primary};
  }

  em, i {
    font-style: italic;
  }

  u {
    text-decoration: underline;
  }

  ul, ol {
    margin: ${appTheme.spacing.xs} 0;
    padding-left: 20px;
  }

  li {
    margin: 4px 0;
    line-height: 1.4;
  }

  ul li {
    list-style-type: disc;
  }

  ol li {
    list-style-type: decimal;
  }

  a {
    color: ${appTheme.colors.primary};
    text-decoration: underline;
    text-decoration-color: rgba(99, 102, 241, 0.5);
    transition: all 0.2s ease;

    &:hover {
      color: ${appTheme.colors.primaryHover};
      text-decoration-color: ${appTheme.colors.primaryHover};
    }

    &:visited {
      color: #8b5cf6;
    }
  }

  code {
    background: rgba(0, 0, 0, 0.08);
    color: #e11d48;
    padding: 2px 4px;
    border-radius: 3px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.9em;
  }

  pre {
    background: rgba(0, 0, 0, 0.05);
    border: 1px solid ${appTheme.colors.border};
    border-radius: ${appTheme.borderRadius.sm};
    padding: ${appTheme.spacing.sm};
    margin: ${appTheme.spacing.xs} 0;
    overflow-x: auto;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 13px;
    line-height: 1.4;

    code {
      background: none;
      color: inherit;
      padding: 0;
      border-radius: 0;
    }
  }

  blockquote {
    border-left: 3px solid ${appTheme.colors.primary};
    margin: ${appTheme.spacing.xs} 0;
    padding: ${appTheme.spacing.xs} ${appTheme.spacing.sm};
    background: rgba(99, 102, 241, 0.05);
    font-style: italic;
    color: ${appTheme.colors.text.secondary};
  }

  hr {
    border: none;
    border-top: 1px solid ${appTheme.colors.border};
    margin: ${appTheme.spacing.md} 0;
  }
`;

const CodeOutput = styled.pre`
  background: #f8f9fa;
  border: 1px solid ${appTheme.colors.border};
  border-radius: ${appTheme.borderRadius.md};
  padding: ${appTheme.spacing.md};
  font-family: monospace;
  font-size: 12px;
  white-space: pre-wrap;
  word-break: break-all;
  max-height: 200px;
  overflow-y: auto;
`;

const testExamples = {
  plainText: 'This is a simple plain text response from the AI assistant.',
  
  withLineBreaks: `This is line 1
This is line 2

This is a new paragraph after double line break`,
  
  withBasicHTML: `<h2>AI Assistant Response</h2>
<p>Here's a response with <strong>bold text</strong> and <em>italic text</em>.</p>
<ul>
<li>First item in list</li>
<li>Second item in list</li>
<li>Third item with <code>inline code</code></li>
</ul>`,
  
  withLinks: `<p>Here are some useful links:</p>
<ul>
<li><a href="https://example.com">Safe HTTPS link</a></li>
<li><a href="mailto:<EMAIL>">Email link</a></li>
<li><a href="javascript:alert('xss')">Dangerous link (should be sanitized)</a></li>
</ul>`,
  
  withCodeBlocks: `<h3>Code Example</h3>
<p>Here's some code:</p>
<pre><code>function hello() {
  console.log("Hello, world!");
}
</code></pre>
<p>And here's some <code>inline code</code> in a sentence.</p>`,
  
  maliciousContent: `<p>This looks innocent</p>
<script>alert('XSS attack!')</script>
<iframe src="https://evil.com/steal-data"></iframe>
<img src="x" onerror="alert('Image XSS')">
<a href="javascript:alert('Link XSS')">Click me</a>
<div onclick="alert('Click XSS')">Clickable div</div>`
};

export default function HTMLTestPage() {
  const [inputContent, setInputContent] = useState(testExamples.withBasicHTML);
  const [processedContent, setProcessedContent] = useState('');

  const handleProcess = () => {
    const result = processAIAssistantContent(inputContent);
    setProcessedContent(result);
  };

  const loadExample = (example: string) => {
    setInputContent(example);
    const result = processAIAssistantContent(example);
    setProcessedContent(result);
  };

  return (
    <Container>
      <Title>AI Assistant HTML Rendering Test</Title>
      
      <Section>
        <SectionTitle>Test Examples</SectionTitle>
        <Button onClick={() => loadExample(testExamples.plainText)}>Plain Text</Button>
        <Button onClick={() => loadExample(testExamples.withLineBreaks)}>Line Breaks</Button>
        <Button onClick={() => loadExample(testExamples.withBasicHTML)}>Basic HTML</Button>
        <Button onClick={() => loadExample(testExamples.withLinks)}>With Links</Button>
        <Button onClick={() => loadExample(testExamples.withCodeBlocks)}>Code Blocks</Button>
        <Button onClick={() => loadExample(testExamples.maliciousContent)}>Malicious Content</Button>
      </Section>

      <Section>
        <SectionTitle>Input Content</SectionTitle>
        <TestInput
          value={inputContent}
          onChange={(e) => setInputContent(e.target.value)}
          placeholder="Enter AI Assistant response content here..."
        />
        <Button onClick={handleProcess}>Process Content</Button>
      </Section>

      {processedContent && (
        <Section>
          <SectionTitle>Results</SectionTitle>
          
          <OutputContainer>
            <OutputLabel>Rendered Output (How it appears in chat)</OutputLabel>
            <RenderedOutput
              dangerouslySetInnerHTML={{ __html: processedContent }}
            />
          </OutputContainer>

          <OutputContainer>
            <OutputLabel>Processed HTML Code</OutputLabel>
            <CodeOutput>{processedContent}</CodeOutput>
          </OutputContainer>
        </Section>
      )}
    </Container>
  );
}
